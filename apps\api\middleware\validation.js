import Joi from 'joi'

// Validation middleware factory
export const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false })

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))

      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      })
    }

    next()
  }
}

// User registration validation schema
export const registerSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  password: Joi.string()
    .min(8)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'Password is required'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required'
    }),

  firstName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'First name must be at least 2 characters long',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),

  lastName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'Last name must be at least 2 characters long',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),

  practiceName: Joi.string()
    .trim()
    .max(255)
    .optional()
    .allow(''),

  licenseNumber: Joi.string()
    .trim()
    .max(100)
    .optional()
    .allow('')
})

// User login validation schema
export const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required'
    })
})

// Password reset request validation schema
export const passwordResetRequestSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
})

// Password reset validation schema
export const passwordResetSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': 'Reset token is required'
    }),

  password: Joi.string()
    .min(8)
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]'))
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': 'Password is required'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required'
    })
})

// Refresh token validation schema
export const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string()
    .required()
    .messages({
      'any.required': 'Refresh token is required'
    })
})

// Profile update validation schema
export const updateProfileSchema = Joi.object({
  firstName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .optional(),

  lastName: Joi.string()
    .trim()
    .min(2)
    .max(50)
    .optional(),

  practiceName: Joi.string()
    .trim()
    .max(255)
    .optional()
    .allow(''),

  licenseNumber: Joi.string()
    .trim()
    .max(100)
    .optional()
    .allow(''),

  bio: Joi.string()
    .trim()
    .max(1000)
    .optional()
    .allow(''),

  phone: Joi.string()
    .trim()
    .pattern(/^[\+]?[1-9][\d]{0,15}$/)
    .optional()
    .allow('')
    .messages({
      'string.pattern.base': 'Please provide a valid phone number'
    }),

  timezone: Joi.string()
    .trim()
    .max(50)
    .optional()
})

// ===================================================================
// 📖 JOURNAL VALIDATION SCHEMAS
// ===================================================================

// Journal entry creation validation schema
export const journalEntrySchema = Joi.object({
  content: Joi.string()
    .trim()
    .min(1)
    .max(10000)
    .required()
    .messages({
      'string.min': 'Journal content cannot be empty',
      'string.max': 'Journal content must be less than 10,000 characters',
      'any.required': 'Journal content is required'
    }),

  title: Joi.string()
    .trim()
    .max(200)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Title must be less than 200 characters'
    }),

  mood: Joi.string()
    .valid(
      'happy', 'calm', 'neutral', 'sad', 'anxious',
      'frustrated', 'thoughtful', 'motivated', 'excited',
      'worried', 'content', 'overwhelmed', 'grateful', 'hopeful'
    )
    .optional()
    .messages({
      'any.only': 'Mood must be one of: happy, calm, neutral, sad, anxious, frustrated, thoughtful, motivated, excited, worried, content, overwhelmed, grateful, hopeful'
    }),

  tags: Joi.array()
    .items(
      Joi.string()
        .trim()
        .min(1)
        .max(50)
        .messages({
          'string.min': 'Each tag must be at least 1 character',
          'string.max': 'Each tag must be less than 50 characters'
        })
    )
    .max(10)
    .optional()
    .messages({
      'array.max': 'Maximum 10 tags allowed'
    })
})

// Journal entry update validation schema
export const journalUpdateSchema = Joi.object({
  content: Joi.string()
    .trim()
    .min(1)
    .max(10000)
    .optional()
    .messages({
      'string.min': 'Journal content cannot be empty',
      'string.max': 'Journal content must be less than 10,000 characters'
    }),

  title: Joi.string()
    .trim()
    .max(200)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Title must be less than 200 characters'
    }),

  mood: Joi.string()
    .valid(
      'happy', 'calm', 'neutral', 'sad', 'anxious',
      'frustrated', 'thoughtful', 'motivated', 'excited',
      'worried', 'content', 'overwhelmed', 'grateful', 'hopeful'
    )
    .optional()
    .messages({
      'any.only': 'Mood must be one of: happy, calm, neutral, sad, anxious, frustrated, thoughtful, motivated, excited, worried, content, overwhelmed, grateful, hopeful'
    }),

  tags: Joi.array()
    .items(
      Joi.string()
        .trim()
        .min(1)
        .max(50)
        .messages({
          'string.min': 'Each tag must be at least 1 character',
          'string.max': 'Each tag must be less than 50 characters'
        })
    )
    .max(10)
    .optional()
    .messages({
      'array.max': 'Maximum 10 tags allowed'
    })
}).min(1).messages({
  'object.min': 'At least one field (content, title, mood, or tags) must be provided for update'
})

// Validation middleware functions for journal
export const validateJournalEntry = validate(journalEntrySchema)
export const validateJournalUpdate = validate(journalUpdateSchema)

// ===================================================================
// 🎯 GRATITUDE VALIDATION (Legacy - keeping for compatibility)
// ===================================================================

export const validateGratitudeEntry = (req, res, next) => {
  const { content } = req.body

  if (!content || typeof content !== 'string' || content.trim().length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Content is required and must be a non-empty string'
    })
  }

  if (content.length > 1000) {
    return res.status(400).json({
      success: false,
      message: 'Content must be less than 1000 characters'
    })
  }

  next()
}
