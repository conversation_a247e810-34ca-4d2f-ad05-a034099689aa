// API configuration and service functions
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api'

// API client class
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL
    this.token = localStorage.getItem('serenity_token')
  }

  // Set authentication token
  setToken(token) {
    this.token = token
    if (token) {
      localStorage.setItem('serenity_token', token)
    } else {
      localStorage.removeItem('serenity_token')
    }
  }

  // Get authentication headers
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    return headers
  }

  // Make HTTP request
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    const config = {
      headers: this.getHeaders(),
      ...options,
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        // Handle token expiration
        if (response.status === 401 && this.token) {
          // Try to refresh token
          const refreshed = await this.refreshToken()
          if (refreshed) {
            // Retry original request with new token
            config.headers = this.getHeaders()
            const retryResponse = await fetch(url, config)
            return await retryResponse.json()
          } else {
            // Refresh failed, redirect to login
            this.handleAuthError()
            throw new Error('Authentication failed')
          }
        }

        throw new Error(data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Handle authentication errors
  handleAuthError() {
    this.setToken(null)
    localStorage.removeItem('serenity_user')
    localStorage.removeItem('serenity_refresh_token')

    // Redirect to login if not already there
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }

  // Refresh access token
  async refreshToken() {
    try {
      const refreshToken = localStorage.getItem('serenity_refresh_token')
      if (!refreshToken) {
        return false
      }

      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      })

      if (response.ok) {
        const data = await response.json()
        this.setToken(data.data.accessToken)
        return true
      } else {
        return false
      }
    } catch (error) {
      console.error('Token refresh failed:', error)
      return false
    }
  }

  // GET request
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' })
  }

  // POST request
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // PUT request
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' })
  }
}

// Create API client instance
const apiClient = new ApiClient()

// Authentication API functions
export const authAPI = {
  // Register new user
  register: async (userData) => {
    const response = await apiClient.post('/auth/register', userData)

    if (response.success && response.data.tokens) {
      // Store tokens and user data
      apiClient.setToken(response.data.tokens.accessToken)
      localStorage.setItem('serenity_refresh_token', response.data.tokens.refreshToken)
      localStorage.setItem('serenity_user', JSON.stringify(response.data.user))
    }

    return response
  },

  // Login user
  login: async (credentials) => {
    const response = await apiClient.post('/auth/login', credentials)

    if (response.success && response.data.tokens) {
      // Store tokens and user data
      apiClient.setToken(response.data.tokens.accessToken)
      localStorage.setItem('serenity_refresh_token', response.data.tokens.refreshToken)
      localStorage.setItem('serenity_user', JSON.stringify(response.data.user))
    }

    return response
  },

  // Logout user
  logout: async () => {
    const refreshToken = localStorage.getItem('serenity_refresh_token')

    try {
      await apiClient.post('/auth/logout', { refreshToken })
    } catch (error) {
      console.error('Logout API call failed:', error)
    } finally {
      // Clear local storage regardless of API call result
      apiClient.setToken(null)
      localStorage.removeItem('serenity_user')
      localStorage.removeItem('serenity_refresh_token')
    }
  },

  // Get user profile
  getProfile: async () => {
    return await apiClient.get('/auth/profile')
  },

  // Verify authentication
  verifyAuth: async () => {
    return await apiClient.get('/auth/verify')
  },

  // Refresh access token
  refreshToken: async () => {
    return await apiClient.refreshToken()
  }
}

// Enhanced Chat API functions with advanced features
export const chatAPI = {
  // Send message to AI therapist with enhanced context
  sendMessage: async (message, conversationId = null, context = {}) => {
    return await apiClient.post('/chat/message', { message, conversationId, context })
  },

  // Get conversation history
  getConversationHistory: async (conversationId) => {
    return await apiClient.get(`/chat/conversation/${conversationId}`)
  },

  // Get user's conversations (requires authentication)
  getUserConversations: async () => {
    return await apiClient.get('/chat/conversations')
  },

  // Delete conversation (requires authentication)
  deleteConversation: async (conversationId) => {
    return await apiClient.delete(`/chat/conversation/${conversationId}`)
  },

  // Advanced AI Features

  // Generate session summary
  generateSessionSummary: async (conversationId, messages = null) => {
    const data = messages ? { messages } : {}
    return await apiClient.post(`/chat/session-summary/${conversationId}`, data)
  },

  // Switch AI role
  switchRole: async (roleId, reason = '') => {
    return await apiClient.post('/chat/switch-role', { roleId, reason })
  },

  // Switch chat mode
  switchMode: async (mode, reason = '') => {
    return await apiClient.post('/chat/switch-mode', { mode, reason })
  },

  // Analyze emotional tone
  analyzeEmotion: async (message) => {
    return await apiClient.post('/chat/analyze-emotion', { message })
  },

  // Get mindfulness exercise
  getMindfulnessExercise: async (emotion = 'neutral', duration = 'short') => {
    return await apiClient.get(`/chat/mindfulness-exercise?emotion=${emotion}&duration=${duration}`)
  },

  // Get smart suggestions
  getSmartSuggestions: async (conversationHistory, emotion = 'neutral') => {
    return await apiClient.post('/chat/smart-suggestions', { conversationHistory, emotion })
  }
}

// Wellness API
export const wellnessAPI = {
  // Get wellness overview
  getOverview: async (emotion = 'neutral') => {
    return await apiClient.get(`/wellness/overview?emotion=${emotion}`)
  },

  // Get personalized breathing exercise
  getBreathingExercise: async (emotion = 'neutral', stressLevel = 'medium', timeAvailable = 5) => {
    return await apiClient.get(`/wellness/breathing?emotion=${emotion}&stressLevel=${stressLevel}&timeAvailable=${timeAvailable}`)
  },

  // Get gratitude prompts (legacy)
  getGratitudePrompts: async (emotion = 'neutral', personalContext = '', count = 3) => {
    return await apiClient.get(`/wellness/gratitude?emotion=${emotion}&personalContext=${encodeURIComponent(personalContext)}&count=${count}`)
  },

  // Advanced CBT Methods
  detectCognitiveDistortions: async (thought, emotion = 'neutral', context = '') => {
    return await apiClient.post('/wellness/cbt/detect-distortions', { thought, emotion, context })
  },

  generateEvidenceQuestions: async (thought, distortions = [], emotion = 'neutral') => {
    return await apiClient.post('/wellness/cbt/evidence-questions', { thought, distortions, emotion })
  },

  generateAdvancedReframe: async (thought, distortions = [], evidenceAnswers = {}, emotion = 'neutral') => {
    return await apiClient.post('/wellness/cbt/advanced-reframe', { thought, distortions, evidenceAnswers, emotion })
  },

  generateInnerCriticDialogue: async (thought, emotion = 'neutral') => {
    return await apiClient.post('/wellness/cbt/inner-critic-dialogue', { thought, emotion })
  },

  generateBeliefAnalysis: async (thoughtHistory = [], currentThought, emotion = 'neutral') => {
    return await apiClient.post('/wellness/cbt/belief-analysis', { thoughtHistory, currentThought, emotion })
  },

  // Get cognitive reframing guidance (legacy)
  getCognitiveReframe: async (negativeThought, emotion = 'neutral') => {
    return await apiClient.post('/wellness/cognitive-reframe', { negativeThought, emotion })
  },

  // Get goal suggestions
  getGoalSuggestions: async (currentGoals = [], mood = 'neutral', timeframe = 'daily') => {
    return await apiClient.post('/wellness/goals', { currentGoals, mood, timeframe })
  },

  // Get focus session guidance
  getFocusSession: async (duration = 25, taskType = 'general', currentMood = 'neutral') => {
    return await apiClient.get(`/wellness/focus?duration=${duration}&taskType=${taskType}&currentMood=${currentMood}`)
  }
}

// Enhanced Gratitude API
export const gratitudeAPI = {
  // Create or get today's gratitude session
  createSession: async (emotion = 'neutral', context = '', timeframe = 'today') => {
    return await apiClient.post('/gratitude/session', { emotion, context, timeframe })
  },

  // Save gratitude response
  saveResponse: async (promptId, responseText, responseTime = 0) => {
    return await apiClient.post('/gratitude/response', { promptId, responseText, responseTime })
  },

  // Generate new prompts for existing session
  generateNewPrompts: async (sessionId, count = 3, context = '') => {
    return await apiClient.post('/gratitude/prompts/generate', { sessionId, count, context })
  },

  // Get session insights and summary
  getSessionInsights: async (sessionId) => {
    return await apiClient.get(`/gratitude/session/${sessionId}/insights`)
  },

  // Get user's gratitude history
  getHistory: async (limit = 30, offset = 0) => {
    return await apiClient.get(`/gratitude/history?limit=${limit}&offset=${offset}`)
  },

  // Create thank you message
  createThankYouMessage: async (sessionId, recipientType, recipientName, messageText, sendMethod = 'journal') => {
    return await apiClient.post('/gratitude/thank-you', {
      sessionId, recipientType, recipientName, messageText, sendMethod
    })
  },

  // Complete gratitude session
  completeSession: async (sessionId, moodAfter, sessionDuration) => {
    return await apiClient.put('/gratitude/session/complete', { sessionId, moodAfter, sessionDuration })
  }
}

// Advanced Journal API with AI Integration
export const journalAPI = {
  // Create new journal entry
  createEntry: async (content, mood = null, title = null, tags = []) => {
    return await apiClient.post('/journal/entries', { content, mood, title, tags })
  },

  // Update existing journal entry
  updateEntry: async (entryId, content, mood = null, title = null, tags = []) => {
    return await apiClient.put(`/journal/entries/${entryId}`, { content, mood, title, tags })
  },

  // Get user's journal entries with pagination and filtering
  getEntries: async (limit = 20, offset = 0, mood = null, dateFrom = null, dateTo = null) => {
    const params = new URLSearchParams({ limit, offset })
    if (mood) params.append('mood', mood)
    if (dateFrom) params.append('dateFrom', dateFrom)
    if (dateTo) params.append('dateTo', dateTo)
    return await apiClient.get(`/journal/entries?${params}`)
  },

  // Get specific journal entry
  getEntry: async (entryId) => {
    return await apiClient.get(`/journal/entries/${entryId}`)
  },

  // Delete journal entry
  deleteEntry: async (entryId) => {
    return await apiClient.delete(`/journal/entries/${entryId}`)
  },

  // AI Analysis Features

  // Generate AI insights for journal entry
  generateInsights: async (entryId, analysisType = 'comprehensive') => {
    return await apiClient.post(`/journal/entries/${entryId}/insights`, { analysisType })
  },

  // Get AI summary of journal entry
  generateSummary: async (entryId, summaryLength = 'medium') => {
    return await apiClient.post(`/journal/entries/${entryId}/summary`, { summaryLength })
  },

  // Analyze emotional patterns across entries
  analyzeEmotionalPatterns: async (timeframe = '30days', entryIds = []) => {
    return await apiClient.post('/journal/analysis/emotional-patterns', { timeframe, entryIds })
  },

  // Detect cognitive patterns and themes
  detectCognitivePatterns: async (entryIds = [], analysisDepth = 'standard') => {
    return await apiClient.post('/journal/analysis/cognitive-patterns', { entryIds, analysisDepth })
  },

  // Get AI writing prompts based on user's journal history
  getAIPrompts: async (mood = 'neutral', context = '', promptType = 'reflection') => {
    return await apiClient.post('/journal/prompts', { mood, context, promptType })
  },

  // AI Interaction Features

  // Start AI conversation about journal entry
  startAIConversation: async (entryId, conversationType = 'reflection') => {
    return await apiClient.post(`/journal/entries/${entryId}/ai-conversation`, { conversationType })
  },

  // Send message in AI journal conversation
  sendAIMessage: async (conversationId, message, context = {}) => {
    return await apiClient.post(`/journal/ai-conversations/${conversationId}/messages`, { message, context })
  },

  // Get AI conversation history
  getAIConversation: async (conversationId) => {
    return await apiClient.get(`/journal/ai-conversations/${conversationId}`)
  },

  // Voice Features

  // Convert voice to text for journaling
  transcribeVoice: async (audioBlob, language = 'en') => {
    const formData = new FormData()
    formData.append('audio', audioBlob)
    formData.append('language', language)
    return await apiClient.post('/journal/voice/transcribe', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Advanced Features

  // Export journal entries
  exportEntries: async (format = 'pdf', entryIds = [], dateRange = null) => {
    return await apiClient.post('/journal/export', { format, entryIds, dateRange })
  },

  // Search journal entries
  searchEntries: async (query, filters = {}) => {
    return await apiClient.post('/journal/search', { query, filters })
  },

  // Get journal statistics and insights
  getJournalStats: async (timeframe = '30days') => {
    return await apiClient.get(`/journal/stats?timeframe=${timeframe}`)
  }
}

// Health check
export const healthAPI = {
  check: async () => {
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`)
      return await response.json()
    } catch (error) {
      console.error('Health check failed:', error)
      throw error
    }
  }
}

export default apiClient
