@import './variables.scss';
@import './wellness.scss';
@import './journal-widget.scss';

// Google Fonts Import
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@400;500;600;700&display=swap');

// CSS Reset and Base Styles
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;

  @include mobile-first(768px) {
    font-size: 18px;
  }
}

body {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-regular);
  line-height: 1.6;
  color: var(--serenity-deep-navy);
  background-color: var(--serenity-cream-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  color: var(--serenity-deep-navy);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-4xl);

  @include mobile-first(768px) {
    font-size: var(--font-size-5xl);
  }
}

h2 {
  font-size: var(--font-size-3xl);

  @include mobile-first(768px) {
    font-size: var(--font-size-4xl);
  }
}

h3 {
  font-size: var(--font-size-2xl);

  @include mobile-first(768px) {
    font-size: var(--font-size-3xl);
  }
}

h4 {
  font-size: var(--font-size-xl);

  @include mobile-first(768px) {
    font-size: var(--font-size-2xl);
  }
}

h5 {
  font-size: var(--font-size-lg);

  @include mobile-first(768px) {
    font-size: var(--font-size-xl);
  }
}

h6 {
  font-size: var(--font-size-base);

  @include mobile-first(768px) {
    font-size: var(--font-size-lg);
  }
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--serenity-warm-gray);
}

a {
  color: var(--serenity-sage-green);
  text-decoration: none;
  transition: color var(--transition-normal);

  &:hover {
    color: var(--serenity-muted-teal);
  }
}

// Minimalistic Button Styles
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-family: var(--font-primary);
  font-weight: var(--font-weight-medium);
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);

  &:focus {
    outline: 2px solid var(--serenity-soft-blue);
    outline-offset: 2px;
  }

  &--primary {
    background: var(--serenity-deep-navy);
    color: var(--white);

    &:hover {
      background: var(--serenity-forest-green);
    }
  }

  &--secondary {
    background: var(--serenity-sage-green);
    color: var(--white);

    &:hover {
      background: var(--serenity-muted-teal);
    }
  }

  &--outline {
    background: transparent;
    color: var(--serenity-deep-navy);
    border: 1px solid var(--serenity-deep-navy);

    &:hover {
      background: var(--serenity-deep-navy);
      color: var(--white);
    }
  }

  &--large {
    padding: 12px 16px;
    font-size: 16px;
  }

  &--small {
    padding: 4px 8px;
    font-size: 12px;
  }

  &--icon {
    padding: 6px;
    border-radius: 6px;

    svg {
      width: 18px;
      height: 18px;
    }
  }
}

// Minimalistic Form Elements
input,
textarea,
select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  font-family: var(--font-primary);
  font-size: 14px;
  transition: border-color var(--transition-normal);

  &:focus {
    outline: none;
    border-color: var(--serenity-sage-green);
  }

  &::placeholder {
    color: var(--gray-500);
  }
}

// Utility Classes
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-sm {
  margin-bottom: var(--spacing-sm);
}

.mb-md {
  margin-bottom: var(--spacing-md);
}

.mb-lg {
  margin-bottom: var(--spacing-lg);
}

.mb-xl {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}

.mt-sm {
  margin-top: var(--spacing-sm);
}

.mt-md {
  margin-top: var(--spacing-md);
}

.mt-lg {
  margin-top: var(--spacing-lg);
}

.mt-xl {
  margin-top: var(--spacing-xl);
}

.hidden {
  display: none;
}

.visible {
  display: block;
}

// Serenity Logo Styles
.serenity-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-heading);
  font-weight: var(--font-weight-bold);
  text-decoration: none;
  color: inherit;

  &__s {
    position: relative;
    width: 36px;
    height: 36px;
    background: var(--serenity-sage-green);
    color: var(--white);
    border-radius: var(--border-radius-md);
    @include flex-center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    box-shadow: 0 2px 8px rgba(122, 155, 142, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 2px;
      right: -4px;
      width: 10px;
      height: 20px;
      background: var(--serenity-muted-teal);
      border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
      opacity: 0.8;
    }
  }

  &__text {
    font-size: var(--font-size-lg);
    color: var(--serenity-deep-navy);
    font-weight: var(--font-weight-bold);
  }

  // Logo variants
  &--modal {
    .serenity-logo__text {
      color: var(--serenity-deep-navy);
    }
  }

  &--auth {
    .serenity-logo__text {
      color: var(--serenity-deep-navy);
    }
  }

  &--loading {
    .serenity-logo__s {
      animation: pulse 2s infinite;
    }
  }
}

// Responsive utilities
.mobile-only {
  @include mobile-first(768px) {
    display: none;
  }
}

.desktop-only {
  display: none;

  @include mobile-first(768px) {
    display: block;
  }
}