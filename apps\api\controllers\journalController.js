import { query } from '../config/database.js'
import geminiService from '../services/geminiService.js'

// ===================================================================
// 📖 JOURNAL CONTROLLER - ADVANCED AI-INTEGRATED JOURNALING
// ===================================================================
// Comprehensive journal management with AI analysis and insights
// Database storage for human-AI interactions and journal entries
// ===================================================================

// Create new journal entry
export const createJournalEntry = async (req, res) => {
  try {
    const { content, mood, title, tags = [] } = req.body
    const userId = req.user.id

    // Validate required fields
    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Journal content is required'
      })
    }

    // Generate title if not provided
    const entryTitle = title || generateTitleFromContent(content)

    // Calculate word count and emotional tone
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
    const emotionalTone = await analyzeEmotionalTone(content)

    // Insert journal entry
    const insertQuery = `
      INSERT INTO journal_entries (
        user_id, title, content, mood, tags, word_count, emotional_tone, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
      RETURNING *
    `

    const result = await query(insertQuery, [
      userId,
      entryTitle,
      content,
      mood,
      JSON.stringify(tags),
      wordCount,
      emotionalTone
    ])

    const journalEntry = result.rows[0]

    // Log the journal creation activity
    await logJournalActivity(userId, 'create', journalEntry.id, {
      wordCount,
      mood,
      emotionalTone
    })

    res.status(201).json({
      success: true,
      message: 'Journal entry created successfully',
      data: {
        ...journalEntry,
        tags: JSON.parse(journalEntry.tags || '[]')
      }
    })

  } catch (error) {
    console.error('Error creating journal entry:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to create journal entry'
    })
  }
}

// Get journal entries with filtering and pagination
export const getJournalEntries = async (req, res) => {
  try {
    const userId = req.user.id
    const {
      limit = 20,
      offset = 0,
      mood,
      dateFrom,
      dateTo,
      searchQuery,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query

    // Build dynamic query
    let whereConditions = ['user_id = $1']
    let queryParams = [userId]
    let paramIndex = 2

    // Add mood filter
    if (mood) {
      whereConditions.push(`mood = $${paramIndex}`)
      queryParams.push(mood)
      paramIndex++
    }

    // Add date range filter
    if (dateFrom) {
      whereConditions.push(`created_at >= $${paramIndex}`)
      queryParams.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      whereConditions.push(`created_at <= $${paramIndex}`)
      queryParams.push(dateTo)
      paramIndex++
    }

    // Add search filter
    if (searchQuery) {
      whereConditions.push(`(title ILIKE $${paramIndex} OR content ILIKE $${paramIndex})`)
      queryParams.push(`%${searchQuery}%`)
      paramIndex++
    }

    // Add pagination parameters
    const limitParam = paramIndex
    const offsetParam = paramIndex + 1
    queryParams.push(parseInt(limit), parseInt(offset))

    const selectQuery = `
      SELECT
        id, title, content, mood, tags, word_count, emotional_tone,
        has_ai_summary, has_ai_insights, created_at, updated_at,
        (SELECT COUNT(*) FROM ai_conversations WHERE journal_entry_id = journal_entries.id) as ai_conversation_count
      FROM journal_entries
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `

    const result = await query(selectQuery, queryParams)

    // Get total count for pagination (exclude limit and offset params)
    const countQuery = `
      SELECT COUNT(*) as total
      FROM journal_entries
      WHERE ${whereConditions.join(' AND ')}
    `
    const countParams = queryParams.slice(0, queryParams.length - 2) // Remove limit and offset
    const countResult = await query(countQuery, countParams)
    const total = parseInt(countResult.rows[0].total)

    // Parse tags for each entry
    const entries = result.rows.map(entry => ({
      ...entry,
      tags: JSON.parse(entry.tags || '[]')
    }))

    res.json({
      success: true,
      data: entries,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        pages: Math.ceil(total / parseInt(limit))
      }
    })

  } catch (error) {
    console.error('Error fetching journal entries:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch journal entries'
    })
  }
}

// Get specific journal entry
export const getJournalEntry = async (req, res) => {
  try {
    const { entryId } = req.params
    const userId = req.user.id

    const selectQuery = `
      SELECT
        je.*,
        (SELECT COUNT(*) FROM ai_conversations WHERE journal_entry_id = je.id) as ai_conversation_count,
        (SELECT json_agg(
          json_build_object(
            'id', ac.id,
            'conversation_type', ac.conversation_type,
            'created_at', ac.created_at
          )
        ) FROM ai_conversations ac WHERE ac.journal_entry_id = je.id) as ai_conversations
      FROM journal_entries je
      WHERE je.id = $1 AND je.user_id = $2
    `

    const result = await query(selectQuery, [entryId, userId])

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Journal entry not found'
      })
    }

    const entry = result.rows[0]
    entry.tags = JSON.parse(entry.tags || '[]')

    res.json({
      success: true,
      data: entry
    })

  } catch (error) {
    console.error('Error fetching journal entry:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch journal entry'
    })
  }
}

// Update journal entry
export const updateJournalEntry = async (req, res) => {
  try {
    const { entryId } = req.params
    const { content, mood, title, tags = [] } = req.body
    const userId = req.user.id

    // Check if entry exists and belongs to user
    const checkQuery = 'SELECT id FROM journal_entries WHERE id = $1 AND user_id = $2'
    const checkResult = await query(checkQuery, [entryId, userId])

    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Journal entry not found'
      })
    }

    // Calculate updated metrics
    const wordCount = content ? content.split(/\s+/).filter(word => word.length > 0).length : null
    const emotionalTone = content ? await analyzeEmotionalTone(content) : null

    // Build dynamic update query
    const updateFields = []
    const updateParams = []
    let paramIndex = 1

    if (title !== undefined) {
      updateFields.push(`title = $${paramIndex}`)
      updateParams.push(title)
      paramIndex++
    }

    if (content !== undefined) {
      updateFields.push(`content = $${paramIndex}`)
      updateParams.push(content)
      paramIndex++

      if (wordCount !== null) {
        updateFields.push(`word_count = $${paramIndex}`)
        updateParams.push(wordCount)
        paramIndex++
      }

      if (emotionalTone !== null) {
        updateFields.push(`emotional_tone = $${paramIndex}`)
        updateParams.push(emotionalTone)
        paramIndex++
      }
    }

    if (mood !== undefined) {
      updateFields.push(`mood = $${paramIndex}`)
      updateParams.push(mood)
      paramIndex++
    }

    if (tags !== undefined) {
      updateFields.push(`tags = $${paramIndex}`)
      updateParams.push(JSON.stringify(tags))
      paramIndex++
    }

    updateFields.push(`updated_at = NOW()`)
    updateParams.push(entryId, userId)

    const updateQuery = `
      UPDATE journal_entries
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex} AND user_id = $${paramIndex + 1}
      RETURNING *
    `

    const result = await query(updateQuery, updateParams)
    const updatedEntry = result.rows[0]

    // Log the update activity
    await logJournalActivity(userId, 'update', entryId, {
      updatedFields: Object.keys(req.body),
      wordCount,
      emotionalTone
    })

    res.json({
      success: true,
      message: 'Journal entry updated successfully',
      data: {
        ...updatedEntry,
        tags: JSON.parse(updatedEntry.tags || '[]')
      }
    })

  } catch (error) {
    console.error('Error updating journal entry:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update journal entry'
    })
  }
}

// Delete journal entry
export const deleteJournalEntry = async (req, res) => {
  try {
    const { entryId } = req.params
    const userId = req.user.id

    // Check if entry exists and belongs to user
    const checkQuery = 'SELECT id, title FROM journal_entries WHERE id = $1 AND user_id = $2'
    const checkResult = await query(checkQuery, [entryId, userId])

    if (checkResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Journal entry not found'
      })
    }

    // Delete related AI conversations first
    await query('DELETE FROM ai_conversations WHERE journal_entry_id = $1', [entryId])

    // Delete the journal entry
    await query('DELETE FROM journal_entries WHERE id = $1 AND user_id = $2', [entryId, userId])

    // Log the deletion activity
    await logJournalActivity(userId, 'delete', entryId, {
      title: checkResult.rows[0].title
    })

    res.json({
      success: true,
      message: 'Journal entry deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting journal entry:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete journal entry'
    })
  }
}

// Helper Functions
const generateTitleFromContent = (content) => {
  if (!content) return 'Untitled Entry'
  const words = content.split(' ').slice(0, 5).join(' ')
  return words.length > 30 ? words.substring(0, 30) + '...' : words
}

const analyzeEmotionalTone = async (content) => {
  try {
    const prompt = `Analyze the emotional tone of this journal entry and respond with just one word from: positive, negative, neutral, anxious, sad, happy, reflective, stressed, grateful, hopeful, frustrated, calm, excited, worried, content, overwhelmed.

Journal entry: "${content}"`

    const response = await geminiService.generateResponse(prompt, [], { context: 'emotion_analysis' })
    return response.content ? response.content.toLowerCase().trim() : 'neutral'
  } catch (error) {
    console.error('Error analyzing emotional tone:', error)
    return 'neutral'
  }
}

const logJournalActivity = async (userId, action, entryId, metadata = {}) => {
  try {
    const insertQuery = `
      INSERT INTO journal_activities (user_id, action, journal_entry_id, metadata, created_at)
      VALUES ($1, $2, $3, $4, NOW())
    `
    await query(insertQuery, [userId, action, entryId, JSON.stringify(metadata)])
  } catch (error) {
    console.error('Error logging journal activity:', error)
    // Don't throw error as this is not critical
  }
}

// ===================================================================
// 🤖 AI ANALYSIS FUNCTIONS
// ===================================================================

// Generate AI summary for journal entry
export const generateJournalSummary = async (req, res) => {
  try {
    const { entryId } = req.params
    const { summaryLength = 'medium' } = req.body
    const userId = req.user.id

    // Get journal entry
    const entryQuery = 'SELECT * FROM journal_entries WHERE id = $1 AND user_id = $2'
    const entryResult = await query(entryQuery, [entryId, userId])

    if (entryResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Journal entry not found'
      })
    }

    const entry = entryResult.rows[0]

    // Generate AI summary
    const summaryPrompt = `As a therapeutic AI assistant, create a ${summaryLength} summary of this journal entry. Focus on key emotions, insights, and themes. Be empathetic and supportive.

Journal Entry: "${entry.content}"

Provide a ${summaryLength === 'short' ? '1-2 sentence' : summaryLength === 'medium' ? '3-4 sentence' : '5-6 sentence'} summary that captures the essence of the entry.`

    const response = await geminiService.generateResponse(summaryPrompt, [], { context: 'journal_summary' })
    const aiSummary = response.content || 'Unable to generate summary at this time.'

    // Store AI summary
    const updateQuery = `
      UPDATE journal_entries
      SET ai_summary = $1, has_ai_summary = true, updated_at = NOW()
      WHERE id = $2 AND user_id = $3
      RETURNING ai_summary
    `

    await query(updateQuery, [aiSummary, entryId, userId])

    // Log AI interaction
    await logAIInteraction(userId, entryId, 'summary', summaryPrompt, aiSummary)

    res.json({
      success: true,
      message: 'AI summary generated successfully',
      data: aiSummary
    })

  } catch (error) {
    console.error('Error generating AI summary:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI summary'
    })
  }
}

// Generate comprehensive AI insights
export const generateJournalInsights = async (req, res) => {
  try {
    const { entryId } = req.params
    const { analysisType = 'comprehensive' } = req.body
    const userId = req.user.id

    // Get journal entry
    const entryQuery = 'SELECT * FROM journal_entries WHERE id = $1 AND user_id = $2'
    const entryResult = await query(entryQuery, [entryId, userId])

    if (entryResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Journal entry not found'
      })
    }

    const entry = entryResult.rows[0]

    // Generate comprehensive insights
    const insightsPrompt = `As a therapeutic AI assistant with expertise in cognitive behavioral therapy and emotional wellness, analyze this journal entry and provide comprehensive insights.

Journal Entry: "${entry.content}"
Mood: ${entry.mood || 'Not specified'}
Emotional Tone: ${entry.emotional_tone || 'Not analyzed'}

Please provide insights in the following format:
{
  "emotionalPatterns": "Analysis of emotional patterns and themes",
  "cognitiveThemes": "Identification of thought patterns and cognitive themes",
  "suggestions": "Therapeutic suggestions and coping strategies",
  "reflectionQuestions": ["Question 1", "Question 2", "Question 3"],
  "strengths": "Positive aspects and strengths identified",
  "growthAreas": "Areas for potential growth and development"
}

Focus on being supportive, non-judgmental, and therapeutically helpful.`

    const response = await geminiService.generateResponse(insightsPrompt, [], { context: 'journal_insights' })
    const aiInsights = response.content || '{"error": "Unable to generate insights at this time."}'

    // Parse AI response as JSON
    let parsedInsights
    try {
      parsedInsights = JSON.parse(aiInsights)
    } catch (parseError) {
      // If JSON parsing fails, create structured response
      parsedInsights = {
        emotionalPatterns: aiInsights,
        cognitiveThemes: "Unable to parse detailed analysis",
        suggestions: "Please try generating insights again",
        reflectionQuestions: ["What emotions did you experience while writing this?"],
        strengths: "Your willingness to reflect and journal",
        growthAreas: "Continue exploring your thoughts and feelings"
      }
    }

    // Store AI insights
    const updateQuery = `
      UPDATE journal_entries
      SET ai_insights = $1, has_ai_insights = true, updated_at = NOW()
      WHERE id = $2 AND user_id = $3
      RETURNING ai_insights
    `

    await query(updateQuery, [JSON.stringify(parsedInsights), entryId, userId])

    // Log AI interaction
    await logAIInteraction(userId, entryId, 'insights', insightsPrompt, JSON.stringify(parsedInsights))

    res.json({
      success: true,
      message: 'AI insights generated successfully',
      data: parsedInsights
    })

  } catch (error) {
    console.error('Error generating AI insights:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI insights'
    })
  }
}

// Analyze emotional patterns across multiple entries
export const analyzeEmotionalPatterns = async (req, res) => {
  try {
    const { timeframe = '30days', entryIds = [] } = req.body
    const userId = req.user.id

    let entriesQuery
    let queryParams = [userId]

    if (entryIds.length > 0) {
      // Analyze specific entries
      const placeholders = entryIds.map((_, index) => `$${index + 2}`).join(',')
      entriesQuery = `
        SELECT content, mood, emotional_tone, created_at
        FROM journal_entries
        WHERE user_id = $1 AND id IN (${placeholders})
        ORDER BY created_at DESC
      `
      queryParams = [userId, ...entryIds]
    } else {
      // Analyze entries within timeframe
      const daysBack = timeframe === '7days' ? 7 : timeframe === '30days' ? 30 : 90
      entriesQuery = `
        SELECT content, mood, emotional_tone, created_at
        FROM journal_entries
        WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '${daysBack} days'
        ORDER BY created_at DESC
        LIMIT 50
      `
    }

    const entriesResult = await query(entriesQuery, queryParams)
    const entries = entriesResult.rows

    if (entries.length === 0) {
      return res.json({
        success: true,
        message: 'No entries found for analysis',
        data: {
          patterns: 'No entries available for pattern analysis',
          trends: 'Insufficient data for trend analysis',
          recommendations: 'Continue journaling to build insights over time'
        }
      })
    }

    // Create analysis prompt
    const analysisPrompt = `As a therapeutic AI assistant, analyze these journal entries for emotional patterns and trends:

${entries.map((entry, index) => `
Entry ${index + 1} (${entry.created_at}):
Mood: ${entry.mood || 'Not specified'}
Emotional Tone: ${entry.emotional_tone || 'Not analyzed'}
Content: "${entry.content.substring(0, 200)}..."
`).join('\n')}

Provide analysis in this format:
{
  "patterns": "Key emotional patterns identified across entries",
  "trends": "Emotional trends and changes over time",
  "recommendations": "Therapeutic recommendations based on patterns",
  "insights": "Additional insights about emotional well-being"
}

Focus on being supportive and providing actionable insights.`

    const response = await geminiService.generateResponse(analysisPrompt, [], { context: 'emotional_patterns' })
    const aiAnalysis = response.content || '{"error": "Unable to analyze patterns at this time."}'

    // Parse AI response
    let parsedAnalysis
    try {
      parsedAnalysis = JSON.parse(aiAnalysis)
    } catch (parseError) {
      parsedAnalysis = {
        patterns: aiAnalysis,
        trends: "Analysis completed",
        recommendations: "Continue regular journaling",
        insights: "Your emotional awareness is growing"
      }
    }

    // Log AI interaction
    await logAIInteraction(userId, null, 'emotional_patterns', analysisPrompt, JSON.stringify(parsedAnalysis))

    res.json({
      success: true,
      message: 'Emotional pattern analysis completed',
      data: parsedAnalysis,
      metadata: {
        entriesAnalyzed: entries.length,
        timeframe,
        analysisDate: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error analyzing emotional patterns:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to analyze emotional patterns'
    })
  }
}

// Helper function to log AI interactions
const logAIInteraction = async (userId, entryId, interactionType, prompt, response) => {
  try {
    const insertQuery = `
      INSERT INTO ai_interactions (
        user_id, journal_entry_id, interaction_type, prompt, response, created_at
      ) VALUES ($1, $2, $3, $4, $5, NOW())
    `
    await query(insertQuery, [userId, entryId, interactionType, prompt, response])
  } catch (error) {
    console.error('Error logging AI interaction:', error)
    // Don't throw error as this is not critical
  }
}
