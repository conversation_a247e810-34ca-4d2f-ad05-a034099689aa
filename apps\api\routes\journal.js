import express from 'express'
import { query } from '../config/database.js'
import { authenticateToken } from '../middleware/auth.js'
import { validateJournalEntry, validateJournalUpdate } from '../middleware/validation.js'
import {
  createJournalEntry,
  getJournalEntries,
  getJournalEntry,
  updateJournalEntry,
  deleteJournalEntry,
  generateJournalSummary,
  generateJournalInsights,
  analyzeEmotionalPatterns
} from '../controllers/journalController.js'

const router = express.Router()

// Helper function for safe JSON parsing
const safeParseJSON = (jsonString, defaultValue = null) => {
  try {
    if (!jsonString || jsonString === 'null' || jsonString === '') {
      return defaultValue
    }
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('Error parsing JSON:', error, 'Input:', jsonString)
    return defaultValue
  }
}

// ===================================================================
// 📖 JOURNAL ROUTES - ADVANCED AI-INTEGRATED JOURNALING
// ===================================================================
// All routes require authentication
// Comprehensive journal management with AI analysis
// ===================================================================

// Apply authentication middleware to all routes
router.use(authenticateToken)

// ===================================================================
// 📝 BASIC JOURNAL OPERATIONS
// ===================================================================

// Create new journal entry
// POST /api/journal/entries
router.post('/entries', validateJournalEntry, createJournalEntry)

// Get journal entries with filtering and pagination
// GET /api/journal/entries?limit=20&offset=0&mood=happy&dateFrom=2024-01-01&dateTo=2024-12-31&searchQuery=therapy
router.get('/entries', getJournalEntries)

// Get specific journal entry
// GET /api/journal/entries/:entryId
router.get('/entries/:entryId', getJournalEntry)

// Update journal entry
// PUT /api/journal/entries/:entryId
router.put('/entries/:entryId', validateJournalUpdate, updateJournalEntry)

// Delete journal entry
// DELETE /api/journal/entries/:entryId
router.delete('/entries/:entryId', deleteJournalEntry)

// ===================================================================
// 🤖 AI ANALYSIS ROUTES
// ===================================================================

// Generate AI summary for journal entry
// POST /api/journal/entries/:entryId/summary
router.post('/entries/:entryId/summary', generateJournalSummary)

// Generate comprehensive AI insights
// POST /api/journal/entries/:entryId/insights
router.post('/entries/:entryId/insights', generateJournalInsights)

// Analyze emotional patterns across entries
// POST /api/journal/analysis/emotional-patterns
router.post('/analysis/emotional-patterns', analyzeEmotionalPatterns)

// ===================================================================
// 📊 JOURNAL STATISTICS AND INSIGHTS
// ===================================================================

// Get journal statistics
// GET /api/journal/stats?timeframe=30days
router.get('/stats', async (req, res) => {
  try {
    const { timeframe = '30days' } = req.query
    const userId = req.user.id

    const daysBack = timeframe === '7days' ? 7 : timeframe === '30days' ? 30 : 90

    // Get basic statistics
    const statsQuery = `
      SELECT
        COUNT(*) as total_entries,
        AVG(word_count) as avg_word_count,
        SUM(word_count) as total_words,
        COUNT(CASE WHEN has_ai_summary = true THEN 1 END) as entries_with_ai_summary,
        COUNT(CASE WHEN has_ai_insights = true THEN 1 END) as entries_with_ai_insights,
        mode() WITHIN GROUP (ORDER BY mood) as most_common_mood,
        mode() WITHIN GROUP (ORDER BY emotional_tone) as most_common_tone
      FROM journal_entries
      WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '${daysBack} days'
    `

    const statsResult = await query(statsQuery, [userId])
    const stats = statsResult.rows[0]

    // Get mood distribution
    const moodQuery = `
      SELECT mood, COUNT(*) as count
      FROM journal_entries
      WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '${daysBack} days' AND mood IS NOT NULL
      GROUP BY mood
      ORDER BY count DESC
    `

    const moodResult = await query(moodQuery, [userId])
    const moodDistribution = moodResult.rows

    // Get writing frequency (entries per day)
    const frequencyQuery = `
      SELECT
        DATE(created_at) as date,
        COUNT(*) as entries_count,
        SUM(word_count) as daily_word_count
      FROM journal_entries
      WHERE user_id = $1 AND created_at >= NOW() - INTERVAL '${daysBack} days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `

    const frequencyResult = await query(frequencyQuery, [userId])
    const writingFrequency = frequencyResult.rows

    res.json({
      success: true,
      data: {
        overview: {
          totalEntries: parseInt(stats.total_entries) || 0,
          averageWordCount: Math.round(parseFloat(stats.avg_word_count)) || 0,
          totalWords: parseInt(stats.total_words) || 0,
          entriesWithAISummary: parseInt(stats.entries_with_ai_summary) || 0,
          entriesWithAIInsights: parseInt(stats.entries_with_ai_insights) || 0,
          mostCommonMood: stats.most_common_mood,
          mostCommonTone: stats.most_common_tone
        },
        moodDistribution,
        writingFrequency,
        timeframe,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error fetching journal statistics:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to fetch journal statistics'
    })
  }
})

// ===================================================================
// 🔍 SEARCH AND EXPORT ROUTES
// ===================================================================

// Search journal entries
// POST /api/journal/search
router.post('/search', async (req, res) => {
  try {
    const { query: searchQuery, filters = {} } = req.body
    const userId = req.user.id

    if (!searchQuery || searchQuery.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      })
    }

    // Build search query with filters
    let whereConditions = ['user_id = $1']
    let queryParams = [userId]
    let paramIndex = 2

    // Add text search
    whereConditions.push(`(title ILIKE $${paramIndex} OR content ILIKE $${paramIndex})`)
    queryParams.push(`%${searchQuery}%`)
    paramIndex++

    // Add filters
    if (filters.mood) {
      whereConditions.push(`mood = $${paramIndex}`)
      queryParams.push(filters.mood)
      paramIndex++
    }

    if (filters.emotionalTone) {
      whereConditions.push(`emotional_tone = $${paramIndex}`)
      queryParams.push(filters.emotionalTone)
      paramIndex++
    }

    if (filters.dateFrom) {
      whereConditions.push(`created_at >= $${paramIndex}`)
      queryParams.push(filters.dateFrom)
      paramIndex++
    }

    if (filters.dateTo) {
      whereConditions.push(`created_at <= $${paramIndex}`)
      queryParams.push(filters.dateTo)
      paramIndex++
    }

    const searchSql = `
      SELECT
        id, title, content, mood, emotional_tone, word_count,
        has_ai_summary, has_ai_insights, created_at,
        ts_rank(to_tsvector('english', title || ' ' || content), plainto_tsquery('english', $2)) as relevance
      FROM journal_entries
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY relevance DESC, created_at DESC
      LIMIT 50
    `

    const result = await query(searchSql, queryParams)

    res.json({
      success: true,
      data: result.rows,
      searchQuery,
      filters,
      resultsCount: result.rows.length
    })

  } catch (error) {
    console.error('Error searching journal entries:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to search journal entries'
    })
  }
})

// Export journal entries
// POST /api/journal/export
router.post('/export', async (req, res) => {
  try {
    const { format = 'json', entryIds = [], dateRange = null } = req.body
    const userId = req.user.id

    let exportQuery
    let queryParams = [userId]

    if (entryIds.length > 0) {
      // Export specific entries
      const placeholders = entryIds.map((_, index) => `$${index + 2}`).join(',')
      exportQuery = `
        SELECT * FROM journal_entries
        WHERE user_id = $1 AND id IN (${placeholders})
        ORDER BY created_at DESC
      `
      queryParams = [userId, ...entryIds]
    } else if (dateRange) {
      // Export entries within date range
      exportQuery = `
        SELECT * FROM journal_entries
        WHERE user_id = $1 AND created_at BETWEEN $2 AND $3
        ORDER BY created_at DESC
      `
      queryParams = [userId, dateRange.from, dateRange.to]
    } else {
      // Export all entries
      exportQuery = `
        SELECT * FROM journal_entries
        WHERE user_id = $1
        ORDER BY created_at DESC
      `
    }

    const result = await query(exportQuery, queryParams)
    const entries = result.rows.map(entry => ({
      ...entry,
      tags: safeParseJSON(entry.tags, []),
      ai_insights: safeParseJSON(entry.ai_insights, null)
    }))

    if (format === 'json') {
      res.json({
        success: true,
        data: entries,
        exportedAt: new Date().toISOString(),
        totalEntries: entries.length
      })
    } else {
      // For other formats (PDF, CSV), you would implement the conversion here
      res.status(400).json({
        success: false,
        message: 'Only JSON format is currently supported'
      })
    }

  } catch (error) {
    console.error('Error exporting journal entries:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to export journal entries'
    })
  }
})

export default router
