import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BookOpen, Plus, Eye, Sparkles, Brain, Heart, MessageCircle,
  Mic, MicOff, Save, X, Edit3, Trash2, Download, Search,
  Calendar, TrendingUp, Zap, Bot, PenTool, FileText
} from 'lucide-react'
import Card from '../shared/Card'
import { journalAPI, chatAPI } from '../../services/api'
import AIWellnessCompanion from '../wellness/AIWellnessCompanion'
import useBodyScrollLock from '../../hooks/useBodyScrollLock'
import useModalKeyboard from '../../hooks/useModalKeyboard'

const JournalingWidget = () => {
  // State management
  const [journalEntries, setJournalEntries] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Modal states
  const [showNewEntryModal, setShowNewEntryModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showAIInsightsModal, setShowAIInsightsModal] = useState(false)
  const [selectedEntry, setSelectedEntry] = useState(null)

  // Entry creation/editing states
  const [entryContent, setEntryContent] = useState('')
  const [entryTitle, setEntryTitle] = useState('')
  const [entryMood, setEntryMood] = useState('')
  const [entryTags, setEntryTags] = useState([])
  const [isEditing, setIsEditing] = useState(false)
  const [editingEntryId, setEditingEntryId] = useState(null)

  // AI features states
  const [aiInsights, setAiInsights] = useState(null)
  const [aiSummary, setAiSummary] = useState(null)
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  const [aiConversation, setAiConversation] = useState([])
  const [aiMessage, setAiMessage] = useState('')

  // Voice recording states
  const [isRecording, setIsRecording] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState(null)
  const [audioChunks, setAudioChunks] = useState([])

  // AI Companion states
  const [showAICompanion, setShowAICompanion] = useState(false)
  const [aiCompanionMessage, setAiCompanionMessage] = useState('')
  const [aiInteractionType, setAiInteractionType] = useState('encouragement')
  const [requiresUserResponse, setRequiresUserResponse] = useState(false)

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [moodFilter, setMoodFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('')

  // Lock body scroll when modals are open
  useBodyScrollLock(showNewEntryModal || showViewModal || showAIInsightsModal)

  // Handle keyboard events for modals
  useModalKeyboard(showNewEntryModal, () => setShowNewEntryModal(false))
  useModalKeyboard(showViewModal, () => setShowViewModal(false))
  useModalKeyboard(showAIInsightsModal, () => setShowAIInsightsModal(false))

  // Mood options for selection
  const moodOptions = [
    { emoji: '😊', label: 'Happy', value: 'happy' },
    { emoji: '😌', label: 'Calm', value: 'calm' },
    { emoji: '😐', label: 'Neutral', value: 'neutral' },
    { emoji: '😔', label: 'Sad', value: 'sad' },
    { emoji: '😰', label: 'Anxious', value: 'anxious' },
    { emoji: '😤', label: 'Frustrated', value: 'frustrated' },
    { emoji: '🤔', label: 'Thoughtful', value: 'thoughtful' },
    { emoji: '💪', label: 'Motivated', value: 'motivated' }
  ]

  // Load journal entries on component mount
  useEffect(() => {
    loadJournalEntries()
  }, [moodFilter, dateFilter])

  // API Functions
  const loadJournalEntries = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await journalAPI.getEntries(20, 0, moodFilter, dateFilter)
      setJournalEntries(response.data || [])
    } catch (error) {
      console.error('Failed to load journal entries:', error)
      setError('Failed to load journal entries')
      // Fallback to mock data for demo
      setJournalEntries([
        {
          id: 1,
          title: 'Mindfulness Reflection',
          content: 'Had an interesting conversation with my therapist today about mindfulness. I\'m starting to understand how my thoughts affect my emotions and how I can observe them without judgment...',
          mood: 'thoughtful',
          wordCount: 245,
          createdAt: new Date().toISOString(),
          hasAISummary: true,
          hasAIInsights: true,
          emotionalTone: 'reflective'
        },
        {
          id: 2,
          title: 'Gratitude Practice',
          content: 'Feeling grateful for the small moments today. The morning coffee, the sunset, and the call with mom made me realize how much beauty surrounds us when we pay attention...',
          mood: 'happy',
          wordCount: 189,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          hasAISummary: true,
          hasAIInsights: false,
          emotionalTone: 'positive'
        },
        {
          id: 3,
          title: 'Work Stress',
          content: 'Work stress is getting to me again. I need to remember the breathing techniques we discussed and maybe try some of the cognitive reframing exercises...',
          mood: 'anxious',
          wordCount: 156,
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          hasAISummary: false,
          hasAIInsights: false,
          emotionalTone: 'stressed'
        }
      ])
    } finally {
      setIsLoading(false)
    }
  }

  const saveJournalEntry = async () => {
    try {
      setIsLoading(true)
      const entryData = {
        content: entryContent,
        mood: entryMood,
        title: entryTitle || generateTitleFromContent(entryContent),
        tags: entryTags
      }

      let response
      if (isEditing && editingEntryId) {
        response = await journalAPI.updateEntry(editingEntryId, entryData.content, entryData.mood, entryData.title, entryData.tags)
      } else {
        response = await journalAPI.createEntry(entryData.content, entryData.mood, entryData.title, entryData.tags)
      }

      if (response.success) {
        await loadJournalEntries()
        resetEntryForm()
        setShowNewEntryModal(false)

        // Trigger AI companion encouragement
        triggerAIInteraction('encouragement',
          `Beautiful entry! I can sense the depth in your reflection. Writing helps us process our thoughts and emotions. How do you feel after expressing yourself? ✨`,
          true
        )
      }
    } catch (error) {
      console.error('Failed to save journal entry:', error)
      setError('Failed to save journal entry')
    } finally {
      setIsLoading(false)
    }
  }

  const generateTitleFromContent = (content) => {
    if (!content) return 'Untitled Entry'
    const words = content.split(' ').slice(0, 5).join(' ')
    return words.length > 30 ? words.substring(0, 30) + '...' : words
  }

  const resetEntryForm = () => {
    setEntryContent('')
    setEntryTitle('')
    setEntryMood('')
    setEntryTags([])
    setIsEditing(false)
    setEditingEntryId(null)
  }

  // Event Handlers
  const handleNewEntry = () => {
    resetEntryForm()
    setShowNewEntryModal(true)
  }

  const handleEditEntry = (entry) => {
    setEntryContent(entry.content)
    setEntryTitle(entry.title)
    setEntryMood(entry.mood)
    setEntryTags(entry.tags || [])
    setIsEditing(true)
    setEditingEntryId(entry.id)
    setShowNewEntryModal(true)
  }

  const handleViewEntry = (entry) => {
    setSelectedEntry(entry)
    setShowViewModal(true)
  }

  const handleDeleteEntry = async (entryId) => {
    if (window.confirm('Are you sure you want to delete this journal entry?')) {
      try {
        await journalAPI.deleteEntry(entryId)
        await loadJournalEntries()
      } catch (error) {
        console.error('Failed to delete entry:', error)
        setError('Failed to delete entry')
      }
    }
  }

  const handleGenerateAISummary = async (entryId) => {
    try {
      setIsGeneratingAI(true)
      const response = await journalAPI.generateSummary(entryId, 'medium')
      setAiSummary(response.data)

      // Update the entry to mark it as having AI summary
      const updatedEntries = journalEntries.map(entry =>
        entry.id === entryId ? { ...entry, hasAISummary: true } : entry
      )
      setJournalEntries(updatedEntries)
    } catch (error) {
      console.error('Failed to generate AI summary:', error)
      setError('Failed to generate AI summary')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  const handleGenerateAIInsights = async (entryId) => {
    try {
      setIsGeneratingAI(true)
      const response = await journalAPI.generateInsights(entryId, 'comprehensive')
      setAiInsights(response.data)
      setSelectedEntry(journalEntries.find(entry => entry.id === entryId))
      setShowAIInsightsModal(true)
    } catch (error) {
      console.error('Failed to generate AI insights:', error)
      setError('Failed to generate AI insights')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  // AI Companion Functions
  const triggerAIInteraction = (type, message, requiresResponse = false) => {
    setAiInteractionType(type)
    setAiCompanionMessage(message)
    setRequiresUserResponse(requiresResponse)
    setShowAICompanion(true)
  }

  const handleAIResponse = (userResponse) => {
    console.log('User responded to AI:', userResponse)
    setShowAICompanion(false)
    setRequiresUserResponse(false)

    // Generate follow-up based on user response
    if (requiresUserResponse) {
      setTimeout(() => {
        if (userResponse.toLowerCase().includes('good') || userResponse.toLowerCase().includes('better') || userResponse.toLowerCase().includes('relieved')) {
          triggerAIInteraction('encouragement', "That's wonderful to hear! Journaling is such a powerful tool for emotional processing. Keep nurturing this practice. 🌱", false)
        } else if (userResponse.toLowerCase().includes('difficult') || userResponse.toLowerCase().includes('hard') || userResponse.toLowerCase().includes('overwhelming')) {
          triggerAIInteraction('support', "I understand. Sometimes putting our thoughts into words can feel challenging. Remember, there's no right or wrong way to journal. You're doing great. 💙", false)
        } else {
          triggerAIInteraction('encouragement', "Thank you for sharing your feelings with me. Your self-awareness is a strength. 🌟", false)
        }
      }, 2000)
    }
  }

  const handleAIDismiss = () => {
    setShowAICompanion(false)
    setRequiresUserResponse(false)
  }

  // Voice Recording Functions
  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const recorder = new MediaRecorder(stream)

      recorder.ondataavailable = (event) => {
        setAudioChunks(prev => [...prev, event.data])
      }

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
        try {
          const response = await journalAPI.transcribeVoice(audioBlob)
          setEntryContent(prev => prev + ' ' + response.data.transcription)
        } catch (error) {
          console.error('Failed to transcribe voice:', error)
        }
        setAudioChunks([])
      }

      setMediaRecorder(recorder)
      recorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Failed to start recording:', error)
      setError('Failed to access microphone')
    }
  }

  const stopVoiceRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop()
      mediaRecorder.stream.getTracks().forEach(track => track.stop())
      setIsRecording(false)
    }
  }

  // Utility Functions
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now - date)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays - 1} days ago`
    return date.toLocaleDateString()
  }

  const getMoodEmoji = (mood) => {
    const moodOption = moodOptions.find(option => option.value === mood)
    return moodOption ? moodOption.emoji : '😐'
  }

  return (
    <>
      <Card className="journal-widget">
        <div className="journal-widget__header">
          <h3 className="widget__title">
            <BookOpen className="widget__icon" />
            Journal Entries
          </h3>
          <button className="widget__action" onClick={handleNewEntry}>
            <Plus size={16} />
            New Entry
          </button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="journal-widget__loading">
            <motion.div
              className="journal-widget__loading-spinner"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles size={24} />
            </motion.div>
            <p>Loading your journal entries...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            className="journal-widget__error"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <p>{error}</p>
            <button onClick={loadJournalEntries} className="journal-widget__retry-btn">
              Try Again
            </button>
          </motion.div>
        )}

        {/* Journal Entries */}
        {!isLoading && !error && (
          <>
            <div className="journal-widget__entries">
              {journalEntries.length === 0 ? (
                <motion.div
                  className="journal-widget__empty"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <BookOpen size={48} className="journal-widget__empty-icon" />
                  <h4>Start Your Journaling Journey</h4>
                  <p>Your thoughts and reflections will appear here. Begin by writing your first entry.</p>
                </motion.div>
              ) : (
                journalEntries.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    className={`journal-widget__entry ${entry.hasAIInsights ? 'journal-widget__entry--ai-analyzed' : ''}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => handleViewEntry(entry)}
                  >
                    <div className="journal-widget__entry-header">
                      <div className="entry-meta">
                        <span className="journal-widget__entry-date">
                          {formatDate(entry.createdAt)}
                        </span>
                        <span className="journal-widget__entry-mood">
                          {getMoodEmoji(entry.mood)}
                        </span>
                      </div>
                      <div className="entry-stats">
                        <span>{entry.wordCount || entry.content?.split(' ').length || 0} words</span>
                        {entry.emotionalTone && (
                          <span className="emotional-tone">{entry.emotionalTone}</span>
                        )}
                      </div>
                    </div>

                    <h4 className="journal-widget__entry-title">{entry.title}</h4>

                    <p className="journal-widget__entry-preview">
                      {entry.content}
                    </p>

                    <div className="journal-widget__entry-actions" onClick={(e) => e.stopPropagation()}>
                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleViewEntry(entry)}
                      >
                        <Eye size={12} />
                        Read Full
                      </button>

                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleEditEntry(entry)}
                      >
                        <Edit3 size={12} />
                        Edit
                      </button>

                      {entry.hasAISummary ? (
                        <button
                          className="journal-widget__entry-action journal-widget__entry-action--ai"
                          onClick={() => handleGenerateAISummary(entry.id)}
                        >
                          <Sparkles size={12} />
                          AI Summary
                        </button>
                      ) : (
                        <button
                          className="journal-widget__entry-action journal-widget__entry-action--ai"
                          onClick={() => handleGenerateAISummary(entry.id)}
                          disabled={isGeneratingAI}
                        >
                          <Brain size={12} />
                          Generate Summary
                        </button>
                      )}

                      <button
                        className="journal-widget__entry-action journal-widget__entry-action--warm"
                        onClick={() => handleGenerateAIInsights(entry.id)}
                        disabled={isGeneratingAI}
                      >
                        <Zap size={12} />
                        AI Insights
                      </button>

                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleDeleteEntry(entry.id)}
                      >
                        <Trash2 size={12} />
                        Delete
                      </button>
                    </div>
                  </motion.div>
                ))
              )}
            </div>

            {/* New Entry Button */}
            <motion.button
              className="journal-widget__new-entry"
              onClick={handleNewEntry}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <PenTool size={20} />
              Start writing your thoughts...
            </motion.button>
          </>
        )}
      </Card>

      {/* New Entry Modal */}
      {showNewEntryModal && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowNewEntryModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <h2>{isEditing ? 'Edit Journal Entry' : 'New Journal Entry'}</h2>
                <button
                  onClick={() => setShowNewEntryModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                {/* Title Input */}
                <div className="journal-form__field">
                  <label htmlFor="entry-title">Title (optional)</label>
                  <input
                    id="entry-title"
                    type="text"
                    value={entryTitle}
                    onChange={(e) => setEntryTitle(e.target.value)}
                    placeholder="Give your entry a title..."
                    className="journal-form__input"
                  />
                </div>

                {/* Mood Selection */}
                <div className="journal-form__field">
                  <label>How are you feeling?</label>
                  <div className="journal-form__mood-grid">
                    {moodOptions.map((mood) => (
                      <button
                        key={mood.value}
                        type="button"
                        className={`journal-form__mood-option ${entryMood === mood.value ? 'journal-form__mood-option--selected' : ''}`}
                        onClick={() => setEntryMood(mood.value)}
                      >
                        <span className="mood-emoji">{mood.emoji}</span>
                        <span className="mood-label">{mood.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Content Textarea */}
                <div className="journal-form__field">
                  <label htmlFor="entry-content">Your thoughts and reflections</label>
                  <div className="journal-form__content-wrapper">
                    <textarea
                      id="entry-content"
                      value={entryContent}
                      onChange={(e) => setEntryContent(e.target.value)}
                      placeholder="Start writing your thoughts... What's on your mind today? How are you feeling? What insights have you discovered?"
                      className="journal-form__textarea"
                      rows={12}
                    />

                    {/* Voice Recording Button */}
                    <button
                      type="button"
                      className={`journal-form__voice-btn ${isRecording ? 'journal-form__voice-btn--recording' : ''}`}
                      onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                    >
                      {isRecording ? <MicOff size={16} /> : <Mic size={16} />}
                      {isRecording ? 'Stop Recording' : 'Voice to Text'}
                    </button>
                  </div>

                  <div className="journal-form__stats">
                    <span>{entryContent.split(' ').filter(word => word.length > 0).length} words</span>
                    <span>{entryContent.length} characters</span>
                  </div>
                </div>

                {/* Tags Input */}
                <div className="journal-form__field">
                  <label htmlFor="entry-tags">Tags (optional)</label>
                  <input
                    id="entry-tags"
                    type="text"
                    value={entryTags.join(', ')}
                    onChange={(e) => setEntryTags(e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                    placeholder="therapy, mindfulness, gratitude, work, relationships..."
                    className="journal-form__input"
                  />
                </div>
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => setShowNewEntryModal(false)}
                  className="journal-btn journal-btn--secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={saveJournalEntry}
                  disabled={!entryContent.trim() || isLoading}
                  className="journal-btn journal-btn--primary"
                >
                  <Save size={16} />
                  {isEditing ? 'Update Entry' : 'Save Entry'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* View Entry Modal */}
      {showViewModal && selectedEntry && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowViewModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content journal-modal__content--large"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <div className="journal-view__meta">
                  <h2>{selectedEntry.title}</h2>
                  <div className="journal-view__details">
                    <span className="journal-view__date">{formatDate(selectedEntry.createdAt)}</span>
                    <span className="journal-view__mood">{getMoodEmoji(selectedEntry.mood)}</span>
                    <span className="journal-view__stats">{selectedEntry.wordCount || selectedEntry.content?.split(' ').length || 0} words</span>
                  </div>
                </div>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                <div className="journal-view__content">
                  {selectedEntry.content}
                </div>

                {/* AI Summary Section */}
                {aiSummary && (
                  <motion.div
                    className="journal-view__ai-section"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <h3>
                      <Sparkles size={20} />
                      AI Summary
                    </h3>
                    <div className="journal-view__ai-content">
                      {aiSummary}
                    </div>
                  </motion.div>
                )}
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => handleEditEntry(selectedEntry)}
                  className="journal-btn journal-btn--secondary"
                >
                  <Edit3 size={16} />
                  Edit Entry
                </button>
                <button
                  type="button"
                  onClick={() => handleGenerateAIInsights(selectedEntry.id)}
                  disabled={isGeneratingAI}
                  className="journal-btn journal-btn--ai"
                >
                  <Brain size={16} />
                  {isGeneratingAI ? 'Generating...' : 'AI Insights'}
                </button>
                <button
                  type="button"
                  onClick={() => handleGenerateAISummary(selectedEntry.id)}
                  disabled={isGeneratingAI}
                  className="journal-btn journal-btn--primary"
                >
                  <Sparkles size={16} />
                  {isGeneratingAI ? 'Generating...' : 'AI Summary'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* AI Insights Modal */}
      {showAIInsightsModal && selectedEntry && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowAIInsightsModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content journal-modal__content--large"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <h2>
                  <Brain size={24} />
                  AI Insights for "{selectedEntry.title}"
                </h2>
                <button
                  onClick={() => setShowAIInsightsModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                {isGeneratingAI ? (
                  <div className="journal-ai__loading">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Brain size={32} />
                    </motion.div>
                    <p>AI is analyzing your journal entry...</p>
                  </div>
                ) : aiInsights ? (
                  <div className="journal-ai__insights">
                    {aiInsights.emotionalPatterns && (
                      <div className="journal-ai__section">
                        <h3>
                          <Heart size={20} />
                          Emotional Patterns
                        </h3>
                        <p>{aiInsights.emotionalPatterns}</p>
                      </div>
                    )}

                    {aiInsights.cognitiveThemes && (
                      <div className="journal-ai__section">
                        <h3>
                          <Brain size={20} />
                          Cognitive Themes
                        </h3>
                        <p>{aiInsights.cognitiveThemes}</p>
                      </div>
                    )}

                    {aiInsights.suggestions && (
                      <div className="journal-ai__section">
                        <h3>
                          <Zap size={20} />
                          Therapeutic Suggestions
                        </h3>
                        <p>{aiInsights.suggestions}</p>
                      </div>
                    )}

                    {aiInsights.reflectionQuestions && (
                      <div className="journal-ai__section">
                        <h3>
                          <MessageCircle size={20} />
                          Reflection Questions
                        </h3>
                        <ul>
                          {aiInsights.reflectionQuestions.map((question, index) => (
                            <li key={index}>{question}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="journal-ai__empty">
                    <Brain size={48} />
                    <p>Click "Generate Insights" to get AI analysis of this entry.</p>
                  </div>
                )}
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => setShowAIInsightsModal(false)}
                  className="journal-btn journal-btn--secondary"
                >
                  Close
                </button>
                {!isGeneratingAI && !aiInsights && (
                  <button
                    type="button"
                    onClick={() => handleGenerateAIInsights(selectedEntry.id)}
                    className="journal-btn journal-btn--primary"
                  >
                    <Brain size={16} />
                    Generate Insights
                  </button>
                )}
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* AI Wellness Companion */}
      <AIWellnessCompanion
        isVisible={showAICompanion}
        message={aiCompanionMessage}
        interactionType={aiInteractionType}
        onResponse={handleAIResponse}
        onDismiss={handleAIDismiss}
        requiresUserResponse={requiresUserResponse}
      />
    </>
  )
}

export default JournalingWidget
