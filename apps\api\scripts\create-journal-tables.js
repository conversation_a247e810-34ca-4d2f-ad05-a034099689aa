import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { query, closePool } from '../config/database.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function createJournalTables() {
  try {
    console.log('🚀 Starting journal tables creation...')

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'create-journal-tables.sql')
    const sql = fs.readFileSync(sqlFilePath, 'utf8')

    // Execute the SQL
    console.log('📊 Executing journal schema SQL...')
    await query(sql)

    console.log('✅ Journal tables created successfully!')
    console.log(`
📖 Journal Database Schema Created:
   ✓ journal_entries - Main journal entries with AI analysis
   ✓ ai_interactions - AI interaction logging
   ✓ ai_conversations - AI conversation management
   ✓ ai_conversation_messages - Individual conversation messages
   ✓ journal_activities - Activity logging
   ✓ journal_prompts - Writing prompts with sample data
   ✓ Indexes and triggers for optimal performance
   ✓ Sample journal prompts for immediate use

🎯 Ready for advanced journaling with AI integration!
    `)

  } catch (error) {
    console.error('❌ Error creating journal tables:', error)
    process.exit(1)
  } finally {
    await closePool()
  }
}

// Run the script
createJournalTables()
