import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FiTrash2, 
  FiAlertTriangle, 
  FiCalendar, 
  FiFileText,
  FiClock
} from 'react-icons/fi'

const DeleteConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  entry,
  isDeleting = false 
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const handleClose = () => {
    if (isDeleting) return // Prevent closing while deleting
    setIsVisible(false)
    setTimeout(onClose, 300) // Wait for animation to complete
  }

  const handleConfirm = () => {
    onConfirm()
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getEntryExcerpt = (content, maxLength = 120) => {
    if (!content) return 'No content'
    return content.length > maxLength 
      ? content.substring(0, maxLength) + '...'
      : content
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="delete-confirmation-overlay"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          onClick={handleClose}
        >
          <motion.div
            className="delete-confirmation-modal"
            initial={{ scale: 0.9, y: 20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.9, y: 20, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="delete-confirmation-header">
              <div className="delete-confirmation-header__icon">
                <FiTrash2 />
              </div>
              <h3 className="delete-confirmation-header__title">
                Delete Journal Entry?
              </h3>
              <p className="delete-confirmation-header__subtitle">
                This action cannot be undone. Your entry and all associated data will be permanently removed.
              </p>
            </div>

            {/* Content */}
            <div className="delete-confirmation-content">
              {/* Entry Preview */}
              {entry && (
                <div className="delete-confirmation-content__entry-preview">
                  <div className="delete-confirmation-content__entry-preview-title">
                    {entry.title || 'Untitled Entry'}
                  </div>
                  <div className="delete-confirmation-content__entry-preview-excerpt">
                    {getEntryExcerpt(entry.content)}
                  </div>
                  <div className="delete-confirmation-content__entry-preview-meta">
                    <span>
                      <FiCalendar />
                      {formatDate(entry.created_at)}
                    </span>
                    {entry.word_count && (
                      <span>
                        <FiFileText />
                        {entry.word_count} words
                      </span>
                    )}
                    {entry.mood && (
                      <span>
                        <FiClock />
                        {entry.mood}
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Warning */}
              <div className="delete-confirmation-content__warning">
                <FiAlertTriangle className="delete-confirmation-content__warning-icon" />
                <div className="delete-confirmation-content__warning-text">
                  <strong>Warning:</strong> This will also delete any AI insights, summaries, and conversation history associated with this entry.
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="delete-confirmation-actions">
              <button
                className="delete-confirmation-actions__button delete-confirmation-actions__button--cancel"
                onClick={handleClose}
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                className={`delete-confirmation-actions__button delete-confirmation-actions__button--delete ${isDeleting ? 'loading' : ''}`}
                onClick={handleConfirm}
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Delete Entry'}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default DeleteConfirmationModal
